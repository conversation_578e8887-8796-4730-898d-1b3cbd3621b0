﻿using PuppeteerSharp;

namespace Amazon2FacebookPoster
{
    public class AmazonLoader<T> where T : class
    {
        private IBrowser? browser;
        private IPage? page;
        private FacebookPostGenerator? _postGenerator;

        public AmazonLoader()
        {
        }

        public void SetGeminiApiKey(string apiKey)
        {
            _postGenerator = new FacebookPostGenerator(apiKey);
        }

        public async Task CloseBrowser()
        {
            if (browser != null)
            {
                await browser.CloseAsync();
            }
        }

        public async Task StartBrowser(bool headless = true, CancellationToken cancellationToken = default)
        {
            await new BrowserFetcher().DownloadAsync();

            // Define user data directory to persist login sessions
            var userDataDir = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "Amazon2FacebookPoster", "ChromeUserData");
            Directory.CreateDirectory(userDataDir);

            var browserArgs = new[] {
                                //"--disable-gpu",  // Disable GPU hardware acceleration
                                //"--disable-dev-shm-usage",  // Prevent running out of memory
                                //"--js-flags=--max-old-space-size=512",  // Limit memory usage
                                //"--single-process",  // Run in single process mode for better performance
                                //"--no-sandbox",
                                //"--disable-setuid-sandbox",
                                //"--start-fullscreen",  // Enable fullscreen mode
                                //"--window-size=1920,1080",
                                //"--disable-features=site-per-process",
                                "--lang=fr-FR"  // Set French language
                            };
            //var launchArgs = JsonSerializer.Serialize(new
            //{
            //    args = browserArgs,
            //    headless = true,  // True headless mode for better performance
            //    waitUntil = "networkidle0"  // Wait for page to be fully loaded
            //});

            //browser = await Puppeteer.ConnectAsync(new ConnectOptions
            //{
            //    BrowserWSEndpoint = $"wss://browserless.atlaprest.com?token=iqV6qATinekY1HZYxToqvbpID7lfueYP&launch={launchArgs}",
            //    DefaultViewport = null,  // Required for fullscreen
            //    AcceptInsecureCerts = true,

            //});
            browser = await Puppeteer.LaunchAsync(new LaunchOptions
            {
                Headless = headless,
                //Devtools = !headless,
                Args = browserArgs,
                UserDataDir = userDataDir,  // Use persistent user data directory to maintain login sessions
#if !DEBUG
                ExecutablePath = "/usr/bin/google-chrome",
#endif
                DefaultViewport = new ViewPortOptions
                {
                    Width = 1920,
                    Height = 1080
                },
                AcceptInsecureCerts = true
            });
            page = await browser.NewPageAsync();
        }

        private async Task TryAndExecute(string link)
        {
            if (browser == null || page == null)
            {
                throw new InvalidOperationException("Le navigateur n'est pas démarré. Appelez StartBrowser() d'abord.");
            }

            try
            {
                if (page.IsClosed)
                    page = await browser.NewPageAsync();
                await page.GoToAsync(link, new NavigationOptions { WaitUntil = new WaitUntilNavigation[] { WaitUntilNavigation.Networkidle0 } });
            }
            catch (NavigationException)
            {
                await Task.Delay(1000);
                await TryAndExecute(link);
            }
        }

        public async Task<T> LoadPage(string link, Func<IPage, Task<T>> action, CancellationToken cancellationToken = default)
        {
            if (page == null)
            {
                throw new InvalidOperationException("Le navigateur n'est pas démarré. Appelez StartBrowser() d'abord.");
            }

            await TryAndExecute(link);
            var result = await action(page);
            return result;
        }

        public async Task<string> LoadPageAndGenerateFacebookPost(string productUrl, string affiliateLink, CancellationToken cancellationToken = default)
        {
            if (_postGenerator == null)
            {
                throw new InvalidOperationException("Clé API Gemini non configurée. Appelez SetGeminiApiKey() d'abord.");
            }

            if (page == null)
            {
                throw new InvalidOperationException("Le navigateur n'est pas démarré. Appelez StartBrowser() d'abord.");
            }

            await TryAndExecute(productUrl);

            // Attendre que la page soit complètement chargée
            await Task.Delay(3000, cancellationToken);

            // Générer le post Facebook basé sur le contenu de la page
            var facebookPost = await _postGenerator.GeneratePostFromPageContent(page, affiliateLink);

            Console.WriteLine("=== POST FACEBOOK GÉNÉRÉ ===");
            Console.WriteLine(facebookPost);
            Console.WriteLine("=== FIN DU POST ===");

            return facebookPost;
        }

        public async Task<string> GenerateFacebookPostFromUrl(string productUrl, string affiliateLink)
        {
            if (_postGenerator == null)
            {
                throw new InvalidOperationException("Clé API Gemini non configurée. Appelez SetGeminiApiKey() d'abord.");
            }

            var facebookPost = await _postGenerator.GeneratePostFromUrl(productUrl, affiliateLink);

            Console.WriteLine("=== POST FACEBOOK GÉNÉRÉ ===");
            Console.WriteLine(facebookPost);
            Console.WriteLine("=== FIN DU POST ===");

            return facebookPost;
        }
    }
}