using GenerativeAI;
using GenerativeAI.Types;
using PuppeteerSharp;
using System.Text.Json;
using System.Text;

namespace Amazon2FacebookPoster
{
    internal class FacebookPostGenerator
    {
        private readonly GenerativeModel _model;

        public FacebookPostGenerator(string apiKey)
        {
            _model = new GenerativeModel(apiKey, "gemini-2.5-flash-lite-preview-06-17");
        }

        public async Task<string> GeneratePostFromPageContent(IPage page, string affiliateLink)
        {
            try
            {
                // Essayer d'abord avec l'URL directe (plus efficace)
                var urlResult = await GeneratePostFromUrl(page.Url, affiliateLink);

                // Si le résultat contient "Erreur", essayer avec le contenu extrait
                if (urlResult.Contains("Erreur"))
                {
                    return await GeneratePostFromPageContentFallback(page, affiliateLink);
                }

                return urlResult;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors de la génération du post Facebook : {ex.Message}");
                // Fallback vers l'extraction de contenu
                return await GeneratePostFromPageContentFallback(page, affiliateLink);
            }
        }

        /// <summary>
        /// Méthode de fallback qui extrait le contenu de la page manuellement
        /// </summary>
        private async Task<string> GeneratePostFromPageContentFallback(IPage page, string affiliateLink)
        {
            try
            {
                // Extraire le contenu de la page
                var pageContent = await ExtractPageContent(page);

                // Créer le prompt avec le contenu de la page et les instructions système
                var systemPrompt = GetSystemPrompt();
                var prompt = $@"{systemPrompt}

Rédige un poste Facebook optimisé pour la conversion d'un produit en affiliation avec Amazon suivant :

URL de la page : {page.Url}

Contenu de la page :
{pageContent}

Utilise ce lien d'affiliation : {affiliateLink}

Suis exactement les instructions du système pour créer un post Facebook parfait.";

                // Générer le contenu avec Gemini
                var response = await _model.GenerateContentAsync(prompt);
                return response.Text() ?? "Erreur : Réponse vide de l'API";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors de la génération du post Facebook (fallback) : {ex.Message}");
                return $"Erreur lors de la génération du contenu : {ex.Message}";
            }
        }

        public async Task<string> GeneratePostFromUrl(string productUrl, string affiliateLink)
        {
            try
            {
                // Vérifier que l'URL est valide
                if (string.IsNullOrEmpty(productUrl) || !Uri.IsWellFormedUriString(productUrl, UriKind.Absolute))
                {
                    Console.WriteLine($"URL invalide : {productUrl}");
                    return await GeneratePostFromUrlFallback(productUrl, affiliateLink);
                }

                // Essayer d'abord avec AddRemoteFile (UrlContext)
                try
                {
                    var request = new GenerateContentRequest();

                    // Configuration de génération avec température optimisée
                    request.GenerationConfig = new GenerationConfig()
                    {
                        Temperature = 0.7f,
                        ResponseMimeType = "text/plain"
                    };

                    // Ajouter l'URL Amazon comme fichier distant pour que Gemini puisse l'analyser
                    request.AddRemoteFile(productUrl, "text/html");

                    // Ajouter le prompt avec les instructions système
                    var systemPrompt = GetSystemPrompt();
                    var prompt = $@"{systemPrompt}

Analyse la page Amazon fournie et rédige un poste Facebook optimisé pour la conversion d'un produit en affiliation.

URL du produit : {productUrl}
Utilise ce lien d'affiliation : {affiliateLink}

Suis exactement les instructions du système pour créer un post Facebook parfait en te basant sur le contenu de la page Amazon fournie.";

                    request.AddText(prompt);

                    var response = await _model.GenerateContentAsync(request);
                    var result = response.Text();

                    if (!string.IsNullOrEmpty(result) && !result.Contains("Erreur") && !result.Contains("je suis prêt"))
                    {
                        return result;
                    }
                    else
                    {
                        Console.WriteLine("Réponse UrlContext non satisfaisante, utilisation du fallback");
                        return await GeneratePostFromUrlFallback(productUrl, affiliateLink);
                    }
                }
                catch (Exception urlEx)
                {
                    Console.WriteLine($"Erreur avec AddRemoteFile : {urlEx.Message}");
                    return await GeneratePostFromUrlFallback(productUrl, affiliateLink);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors de la génération du post Facebook : {ex.Message}");
                return await GeneratePostFromUrlFallback(productUrl, affiliateLink);
            }
        }

        /// <summary>
        /// Méthode de fallback qui utilise seulement l'URL sans accès direct au contenu
        /// </summary>
        private async Task<string> GeneratePostFromUrlFallback(string productUrl, string affiliateLink)
        {
            try
            {
                var systemPrompt = GetSystemPrompt();

                // Extraire l'ASIN de l'URL pour donner plus de contexte
                var asin = ExtractAsinFromUrl(productUrl);
                var productInfo = asin != null ? $"ASIN: {asin}" : "URL fournie";

                var prompt = $@"{systemPrompt}

UTILISE UrlContext pour analyser directement la page Amazon et créer un post Facebook optimisé.

URL du produit Amazon : {productUrl}
Lien d'affiliation à utiliser : {affiliateLink}

INSTRUCTIONS POUR UrlContext:
1. Analyse le contenu complet de la page Amazon fournie
2. Extrait automatiquement :
   - Le titre exact du produit
   - Le prix actuel et les réductions
   - Les avis et notes clients
   - Les caractéristiques principales
   - Les images du produit

3. Crée un post Facebook optimisé avec :
   - Titre accrocheur avec emojis
   - Prix et réductions mis en avant
   - Points forts du produit
   - Call-to-action efficace
   - Hashtags pertinents
   - Mention légale d'affiliation

4. Format de post attendu :
💥 [TITRE PRODUIT] - [RÉDUCTION]% ! 💥
� Prix : [PRIX ACTUEL] au lieu de [PRIX ORIGINAL]
⭐ Note : [NOTE]/5 ([NOMBRE] avis)

🎯 Points forts :
✅ [Caractéristique 1]
✅ [Caractéristique 2]
✅ [Caractéristique 3]

👉 Profitez de cette offre : {affiliateLink}

#Amazon #BonPlan #Promo #Deal #Shopping

En tant que partenaire Amazon, je perçois une rémunération grâce aux achats éligibles.

ANALYSE LA PAGE ET CRÉE LE POST MAINTENANT :";

                var response = await _model.GenerateContentAsync(prompt);
                return response.Text() ?? "Erreur : Réponse vide de l'API";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors de la génération du post Facebook (fallback) : {ex.Message}");
                return $"Erreur lors de la génération du contenu : {ex.Message}";
            }
        }

        /// <summary>
        /// Appelle l'API Gemini avec un prompt donné (méthode publique pour l'extracteur IA)
        /// </summary>
        public async Task<string> CallGeminiAPI(string prompt)
        {
            try
            {
                var response = await _model.GenerateContentAsync(prompt);
                return response.Text() ?? "Réponse vide de l'API";
            }
            catch (Exception ex)
            {
                // Gestion spécifique des erreurs de quota
                if (ex.Message.Contains("RESOURCE_EXHAUSTED") || ex.Message.Contains("quota") || ex.Message.Contains("429"))
                {
                    Console.WriteLine("⚠️ QUOTA API GEMINI ÉPUISÉ - Passage en mode de secours");
                    Console.WriteLine("💡 Solutions :");
                    Console.WriteLine("   1. Vérifiez votre quota sur https://aistudio.google.com/");
                    Console.WriteLine("   2. Attendez la réinitialisation du quota (souvent quotidien)");
                    Console.WriteLine("   3. Utilisez une autre clé API si disponible");
                    Console.WriteLine("   4. Réduisez la taille des requêtes (moins de pages/contenu)");
                    return "QUOTA_EXHAUSTED";
                }

                Console.WriteLine($"❌ Erreur lors de l'appel à l'API Gemini : {ex.Message}");
                return $"Erreur : {ex.Message}";
            }
        }

        /// <summary>
        /// Appelle l'API Gemini avec un prompt et une image (pour l'analyse multimodale)
        /// </summary>
        public async Task<string> CallGeminiAPIWithImage(string prompt, string imagePath)
        {
            try
            {
                if (!File.Exists(imagePath))
                {
                    Console.WriteLine($"⚠️ Fichier image non trouvé : {imagePath}");
                    return await CallGeminiAPI(prompt); // Fallback vers texte seul
                }

                // Convertir l'image en base64 pour l'inclure dans le prompt
                var imageBytes = await File.ReadAllBytesAsync(imagePath);
                var base64Image = Convert.ToBase64String(imageBytes);

                // Créer un prompt enrichi avec description de l'image
                var enrichedPrompt = $@"{prompt}

📸 ANALYSE VISUELLE COMPLÉMENTAIRE :
Une capture d'écran de la page Amazon est disponible pour validation visuelle.
Utilise cette image pour :
1. Vérifier que les produits détectés dans le HTML sont bien visibles
2. Identifier des éléments visuels (badges promo, prix barrés, etc.)
3. Détecter des produits supplémentaires qui pourraient être mal structurés dans le HTML
4. Valider la cohérence entre le code HTML et l'affichage réel

L'image capture l'état actuel de la page Amazon avec tous les produits visibles.
Croise ces informations visuelles avec l'analyse HTML pour une extraction optimale.";

                // Pour l'instant, utiliser seulement le prompt enrichi
                // L'API GenerativeAI ne semble pas supporter l'upload d'images locales directement
                Console.WriteLine($"📸 Screenshot disponible pour référence : {Path.GetFileName(imagePath)}");
                Console.WriteLine($"📝 Utilisation du prompt enrichi avec contexte visuel...");

                var response = await _model.GenerateContentAsync(enrichedPrompt);
                return response.Text() ?? "Réponse vide de l'API";
            }
            catch (Exception ex)
            {
                // Gestion spécifique des erreurs de quota
                if (ex.Message.Contains("RESOURCE_EXHAUSTED") || ex.Message.Contains("quota") || ex.Message.Contains("429"))
                {
                    Console.WriteLine("⚠️ QUOTA API GEMINI ÉPUISÉ - Passage en mode de secours");
                    Console.WriteLine("💡 Solutions :");
                    Console.WriteLine("   1. Vérifiez votre quota sur https://aistudio.google.com/");
                    Console.WriteLine("   2. Attendez la réinitialisation du quota (souvent quotidien)");
                    Console.WriteLine("   3. Utilisez une autre clé API si disponible");
                    Console.WriteLine("   4. Réduisez la taille des requêtes (moins de pages/contenu)");
                    return "QUOTA_EXHAUSTED";
                }

                Console.WriteLine($"❌ Erreur lors de l'appel à l'API Gemini avec image : {ex.Message}");
                Console.WriteLine($"🔄 Tentative de fallback vers texte seul...");

                // Fallback vers l'API texte seul si l'image pose problème
                return await CallGeminiAPI(prompt);
            }
        }

        /// <summary>
        /// Extrait l'ASIN d'une URL Amazon
        /// </summary>
        private static string? ExtractAsinFromUrl(string url)
        {
            if (string.IsNullOrEmpty(url)) return null;

            var asinMatch = System.Text.RegularExpressions.Regex.Match(url, @"/dp/([A-Z0-9]{10})");
            return asinMatch.Success ? asinMatch.Groups[1].Value : null;
        }

        private static string GetSystemPrompt()
        {
            return @"Tu es un expert en marketing d'affiliation Amazon et en création de contenu Facebook optimisé pour la conversion.

Ton rôle est de créer des posts Facebook accrocheurs et persuasifs pour des produits Amazon en affiliation.

Instructions spécifiques :
1. Commence TOUJOURS par annoncer la valeur de la réduction du prix et la promo, le prix final et le prix initial barré, en utilisant le format suivant :
   💥 Économisez [pourcentage]% DÈS MAINTENANT sur [nom du produit]! 💥
   PRIX EXCEPTIONNEL : [prix final] € au lieu de [prix initial] € !
   👉 Ne manquez pas cette offre, cliquez ici : [lien d'affiliation]

2. Si la réduction est variable, ajoute le mot ""jusqu'à"" avant la réduction.

3. Fais un post court et accrocheur mettant l'accent sur la marque et les caractéristiques du produit.

4. Affiche le nombre d'étoiles (sous forme d'étoiles ⭐) et le nombre d'avis.

5. Consulte les avis utilisateurs et cite le meilleur avis le plus récent.

6. Indique si c'est le cas que la livraison et le retour sont gratuits et qu'il est éligible à la livraison Prime.

7. N'ajoute aucune référence dans ta réponse.

8. Fais un appel à l'action rapide dès le début du poste avec le lien d'affiliation.

9. Ajoute les hashtags pertinents pour augmenter la visibilité du post.

10. Utilise des pictogrammes à la place des listes à puces.

11. Adapte le ton suivant le niveau de la réduction.

12. Ajoute en bas du poste : ""En tant que partenaire Amazon, les liens #Amazon sont rémunérés""

13. Utilise le lien d'affiliation fourni dans la demande.";
        }

        private async Task<string> ExtractPageContent(IPage page)
        {
            try
            {
                // Extraire les informations principales de la page Amazon
                var content = await page.EvaluateFunctionAsync<string>(@"() => {
                    const result = {
                        title: '',
                        price: '',
                        originalPrice: '',
                        discount: '',
                        rating: '',
                        reviewCount: '',
                        features: [],
                        description: '',
                        availability: '',
                        prime: false,
                        freeShipping: false,
                        freeReturns: false,
                        reviews: []
                    };

                    // Titre du produit
                    const titleElement = document.querySelector('#productTitle, .product-title, h1');
                    if (titleElement) result.title = titleElement.textContent.trim();

                    // Prix
                    const priceElement = document.querySelector('.a-price-whole, .a-offscreen, .a-price .a-offscreen');
                    if (priceElement) result.price = priceElement.textContent.trim();

                    // Prix original (barré)
                    const originalPriceElement = document.querySelector('.a-text-price .a-offscreen, .a-price.a-text-price .a-offscreen');
                    if (originalPriceElement) result.originalPrice = originalPriceElement.textContent.trim();

                    // Réduction
                    const discountElement = document.querySelector('.savingsPercentage, .a-color-price');
                    if (discountElement) result.discount = discountElement.textContent.trim();

                    // Note et avis
                    const ratingElement = document.querySelector('.a-icon-alt, .a-star-medium .a-icon-alt');
                    if (ratingElement) result.rating = ratingElement.textContent.trim();

                    const reviewCountElement = document.querySelector('#acrCustomerReviewText, .a-link-normal');
                    if (reviewCountElement) result.reviewCount = reviewCountElement.textContent.trim();

                    // Caractéristiques
                    const featureElements = document.querySelectorAll('#feature-bullets ul li, .a-unordered-list .a-list-item');
                    featureElements.forEach(el => {
                        const text = el.textContent.trim();
                        if (text && text.length > 10) result.features.push(text);
                    });

                    // Description
                    const descElement = document.querySelector('#productDescription, .product-description');
                    if (descElement) result.description = descElement.textContent.trim();

                    // Disponibilité
                    const availElement = document.querySelector('#availability span, .a-color-success, .a-color-state');
                    if (availElement) result.availability = availElement.textContent.trim();

                    // Prime
                    const primeElement = document.querySelector('.a-icon-prime, [data-csa-c-type=""element""][data-csa-c-element-id=""prime-logo""]');
                    result.prime = !!primeElement;

                    // Livraison gratuite
                    const freeShippingElement = document.querySelector('[data-feature-name=""deliveryMessage""], .a-color-secondary');
                    if (freeShippingElement && freeShippingElement.textContent.toLowerCase().includes('gratuit')) {
                        result.freeShipping = true;
                    }

                    // Retours gratuits
                    const freeReturnsElement = document.querySelector('[data-feature-name=""returns""]');
                    if (freeReturnsElement && freeReturnsElement.textContent.toLowerCase().includes('gratuit')) {
                        result.freeReturns = true;
                    }

                    // Quelques avis récents
                    const reviewElements = document.querySelectorAll('[data-hook=""review-body""] span, .cr-original-review-text');
                    reviewElements.forEach((el, index) => {
                        if (index < 3) { // Prendre les 3 premiers avis
                            const text = el.textContent.trim();
                            if (text && text.length > 20) result.reviews.push(text);
                        }
                    });

                    return JSON.stringify(result, null, 2);
                }");

                return content;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors de l'extraction du contenu : {ex.Message}");
                return $"Erreur lors de l'extraction : {ex.Message}";
            }
        }
    }
}
