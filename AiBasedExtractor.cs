using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Text.Json;
using System.IO;
using PuppeteerSharp;

namespace Amazon2FacebookPoster
{
    /// <summary>
    /// Extracteur basé sur l'IA qui utilise Gemini pour analyser le contenu HTML
    /// sans dépendre des sélecteurs CSS qui changent constamment
    /// </summary>
    public class AiBasedExtractor
    {
        private readonly AmazonLoader<string> _amazonLoader;
        private FacebookPostGenerator? _postGenerator;
        private List<ProductInfo> _extractedProducts;

        public AiBasedExtractor(AmazonLoader<string> amazonLoader)
        {
            _amazonLoader = amazonLoader;
            _extractedProducts = new List<ProductInfo>();
        }

        public void SetGeminiApiKey(string apiKey)
        {
            _postGenerator = new FacebookPostGenerator(apiKey);
        }

        /// <summary>
        /// Extrait les produits en utilisant l'IA pour analyser le HTML
        /// </summary>
        public async Task<List<ProductInfo>> ExtractProductsWithAI(int maxPages = 1, int maxLoadMoreClicks = 5)
        {
            if (_postGenerator == null)
            {
                throw new InvalidOperationException("Clé API Gemini non configurée. Appelez SetGeminiApiKey() d'abord.");
            }

            var dealsUrl = "https://www.amazon.fr/deals?ref_=nav_cs_gb";
            _extractedProducts.Clear();

            Console.WriteLine("🤖 Début de l'extraction basée sur l'IA...");
            Console.WriteLine($"📋 Configuration: {maxPages} pages max, {maxLoadMoreClicks} clics 'Afficher plus' max");

            await _amazonLoader.LoadPage(dealsUrl, async (page) =>
            {
                // Détecter le type de pagination
                var paginationType = await DetectPaginationType(page);
                Console.WriteLine($"🔍 Type de pagination détecté : {paginationType}");

                if (paginationType == "infinite_scroll")
                {
                    await HandleInfiniteScroll(page, maxPages, maxLoadMoreClicks);
                }
                else
                {
                    await HandleTraditionalPagination(page, maxPages);
                }

                Console.WriteLine($"🎯 Extraction IA terminée ! Total : {_extractedProducts.Count} produits");
                return "";
            });

            return _extractedProducts;
        }

        /// <summary>
        /// Détecte les informations de pagination (nombre de pages)
        /// </summary>
        private async Task<string> DetectPageInfo(IPage page)
        {
            try
            {
                // Chercher les indicateurs de pagination comme "Page 1 sur 6"
                var pageIndicators = await page.QuerySelectorAllAsync("*");

                foreach (var element in pageIndicators)
                {
                    try
                    {
                        var textContent = await element.GetPropertyAsync("textContent");
                        var text = await textContent.JsonValueAsync<string>();

                        if (!string.IsNullOrEmpty(text))
                        {
                            // Patterns pour détecter "Page X sur Y"
                            var pagePattern = @"Page\s+(\d+)\s+sur\s+(\d+)";
                            var match = System.Text.RegularExpressions.Regex.Match(text, pagePattern, System.Text.RegularExpressions.RegexOptions.IgnoreCase);

                            if (match.Success)
                            {
                                var currentPage = match.Groups[1].Value;
                                var totalPages = match.Groups[2].Value;
                                return $"Page {currentPage} sur {totalPages} détectée";
                            }
                        }
                    }
                    catch
                    {
                        // Ignorer les erreurs de lecture du texte
                    }
                }

                return "";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Erreur détection info pagination : {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// Détecte le type de pagination sur la page
        /// </summary>
        private async Task<string> DetectPaginationType(IPage page)
        {
            try
            {
                // Chercher des éléments de pagination traditionnelle
                var paginationElements = await page.QuerySelectorAllAsync("a[aria-label*='page'], .a-pagination a, .s-pagination-item");

                // Chercher des indicateurs d'infinite scroll
                var infiniteScrollIndicators = await page.QuerySelectorAllAsync("[data-testid*='infinite'], [class*='infinite'], [id*='infinite']");

                // Vérifier la présence de boutons "Voir plus" ou "Charger plus"
                var allButtons = await page.QuerySelectorAllAsync("button");
                var loadMoreButtonsCount = 0;

                foreach (var button in allButtons)
                {
                    try
                    {
                        var buttonText = await button.GetPropertyAsync("textContent");
                        var text = await buttonText.JsonValueAsync<string>();
                        if (text != null && (text.Contains("Voir plus") || text.Contains("Charger plus") || text.Contains("Load more")))
                        {
                            loadMoreButtonsCount++;
                        }
                    }
                    catch
                    {
                        // Ignorer les erreurs de lecture du texte
                    }
                }

                if (paginationElements.Length > 0)
                {
                    Console.WriteLine($"🔍 Pagination traditionnelle détectée ({paginationElements.Length} éléments)");
                    return "traditional";
                }
                else if (infiniteScrollIndicators.Length > 0 || loadMoreButtonsCount > 0)
                {
                    Console.WriteLine($"🔍 Infinite scroll détecté ({loadMoreButtonsCount} boutons 'Voir plus')");
                    return "infinite_scroll";
                }
                else
                {
                    // Par défaut, essayer l'infinite scroll
                    Console.WriteLine($"🔍 Type de pagination incertain, tentative d'infinite scroll");
                    return "infinite_scroll";
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Erreur détection pagination : {ex.Message}");
                return "infinite_scroll"; // Fallback
            }
        }

        /// <summary>
        /// Gère l'infinite scroll pour charger plus de produits
        /// CORRECTION : Sépare les clics "Afficher plus" de la pagination des pages
        /// </summary>
        private async Task HandleInfiniteScroll(IPage page, int maxPages, int maxLoadMoreClicks = 5)
        {
            var loadMoreClickCount = 0;
            var stableCount = 0;
            int pageNum = 1; // Déclarer pageNum ici pour qu'il soit accessible après la boucle

            // PHASE 1: Charger le maximum de contenu avec les clics "Afficher plus"
            Console.WriteLine($"🔄 PHASE 1: Chargement du contenu (max {maxLoadMoreClicks} clics 'Afficher plus')");

            while (loadMoreClickCount < maxLoadMoreClicks)
            {
                // Détecter le nombre de pages disponibles
                var pageInfo = await DetectPageInfo(page);
                if (!string.IsNullOrEmpty(pageInfo))
                {
                    Console.WriteLine($"📄 {pageInfo}");
                }

                // Scroll et chercher les boutons "Afficher plus"
                var foundLoadMoreButton = await ScrollToLoadMore(page, maxLoadMoreClicks, loadMoreClickCount);

                if (foundLoadMoreButton)
                {
                    loadMoreClickCount++;
                    Console.WriteLine($"✅ Clic {loadMoreClickCount}/{maxLoadMoreClicks} effectué");

                    // Attendre que le nouveau contenu se charge
                    await Task.Delay(5000);
                }
                else
                {
                    Console.WriteLine("🔍 Plus de boutons 'Afficher plus' trouvés");
                    break;
                }

                // Délai entre les clics pour simuler comportement humain
                var delay = new Random().Next(2000, 5000);
                await Task.Delay(delay);
            }

            // PHASE 2: Analyser le contenu chargé par sections
            Console.WriteLine($"🧠 PHASE 2: Analyse IA du contenu chargé (max {maxPages} analyses)");

            for (pageNum = 1; pageNum <= maxPages; pageNum++)
            {
                Console.WriteLine($"📄 Analyse IA section {pageNum}/{maxPages}...");

                // Scroll pour s'assurer que tout le contenu est visible
                await page.EvaluateFunctionAsync(@"
                    () => {
                        window.scrollTo(0, document.body.scrollHeight);
                    }");

                await Task.Delay(2000);

                // Extraire le HTML de la page
                var htmlContent = await page.GetContentAsync();

                // Nettoyer le HTML pour l'IA
                var cleanedHtml = CleanHtmlForAI(htmlContent);

                Console.WriteLine($"📊 HTML nettoyé : {cleanedHtml.Length:N0} caractères");

                // Analyser avec l'IA
                var products = await ExtractProductsWithGemini(cleanedHtml);

                // Éviter les doublons en comparant les URLs et améliorer la détection
                var newProducts = products.Where(p => !_extractedProducts.Any(existing =>
                    existing.ProductUrl == p.ProductUrl ||
                    existing.Title == p.Title)).ToList();

                _extractedProducts.AddRange(newProducts);

                Console.WriteLine($"✅ {newProducts.Count} nouveaux produits extraits (analyse {pageNum})");
                Console.WriteLine($"📊 Total cumulé : {_extractedProducts.Count} produits uniques");

                // Afficher quelques exemples des nouveaux produits
                if (newProducts.Count > 0)
                {
                    Console.WriteLine("📋 Nouveaux produits détectés :");
                    for (int i = 0; i < Math.Min(3, newProducts.Count); i++)
                    {
                        var product = newProducts[i];
                        Console.WriteLine($"   • {product.Title.Substring(0, Math.Min(60, product.Title.Length))}...");
                    }
                    if (newProducts.Count > 3)
                    {
                        Console.WriteLine($"   ... et {newProducts.Count - 3} autres produits");
                    }
                }

                // Vérifier si on a atteint un plateau (pas de nouveaux produits)
                if (newProducts.Count == 0)
                {
                    stableCount++;
                    Console.WriteLine($"⚠️ Aucun nouveau produit dans cette analyse ({stableCount}/2)");
                    if (stableCount >= 2)
                    {
                        Console.WriteLine("⚠️ Plus de nouveaux produits détectés, arrêt des analyses");
                        break;
                    }
                }
                else
                {
                    stableCount = 0;
                }

                // Délai entre les analyses pour éviter la surcharge
                if (pageNum < maxPages)
                {
                    var delay = new Random().Next(3000, 8000);
                    Console.WriteLine($"⏳ Attente de {delay / 1000}s avant la prochaine analyse...");
                    await Task.Delay(delay);
                }
            }

            // Afficher les informations de pagination finales
            var finalPageInfo = await DetectPageInfo(page);
            if (!string.IsNullOrEmpty(finalPageInfo))
            {
                Console.WriteLine($"📄 Information finale : {finalPageInfo}");
            }

            Console.WriteLine($"📊 RÉSUMÉ FINAL:");
            Console.WriteLine($"   🔄 Clics 'Afficher plus' effectués: {loadMoreClickCount}/{maxLoadMoreClicks}");
            Console.WriteLine($"   🧠 Analyses IA effectuées: {Math.Min(pageNum -1, maxPages)}/{maxPages}");
            Console.WriteLine($"   📦 Total produits uniques: {_extractedProducts.Count}");
        }

        /// <summary>
        /// Scroll progressif pour charger plus de contenu et gérer les boutons "Afficher plus"
        /// </summary>
        private async Task<bool> ScrollToLoadMore(IPage page, int maxLoadMoreClicks, int currentClickCount)
        {
            bool foundLoadMoreButton = false;

            try
            {
                // Scroll progressif vers le bas
                await page.EvaluateFunctionAsync(@"
                    async () => {
                        const scrollStep = 500;
                        const scrollDelay = 200;
                        const maxScrolls = 10;

                        for (let i = 0; i < maxScrolls; i++) {
                            window.scrollBy(0, scrollStep);
                            await new Promise(resolve => setTimeout(resolve, scrollDelay));
                        }

                        // Scroll final jusqu'en bas
                        window.scrollTo(0, document.body.scrollHeight);
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    }");

                // Chercher et cliquer sur les boutons "Afficher plus" s'ils existent et si on n'a pas atteint la limite
                if (currentClickCount < maxLoadMoreClicks)
                {
                    var loadMoreButtons = await FindLoadMoreButtons(page);

                    if (loadMoreButtons.Count > 0)
                    {
                        var button = loadMoreButtons.First();
                        var buttonText = await GetButtonText(button);

                        var isVisible = await button.IsVisibleAsync();
                        if (isVisible)
                        {
                            Console.WriteLine($"🔄 Clic {currentClickCount + 1}/{maxLoadMoreClicks} sur bouton '{buttonText}'...");
                            await button.ClickAsync();
                            await Task.Delay(3000); // Attendre plus longtemps après un clic
                            foundLoadMoreButton = true;
                        }
                    }
                }
                else if (currentClickCount >= maxLoadMoreClicks)
                {
                    // Vérifier s'il y a encore des boutons disponibles
                    var remainingButtons = await FindLoadMoreButtons(page);
                    if (remainingButtons.Count > 0)
                    {
                        Console.WriteLine($"⚠️ Bouton 'Afficher plus' détecté mais limite de clics atteinte ({maxLoadMoreClicks})");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Erreur lors du scroll : {ex.Message}");
            }

            return foundLoadMoreButton;
        }

        /// <summary>
        /// Trouve tous les boutons "Afficher plus d'offres" sur la page
        /// </summary>
        private async Task<List<IElementHandle>> FindLoadMoreButtons(IPage page)
        {
            var loadMoreButtons = new List<IElementHandle>();

            try
            {
                // Patterns pour les boutons "Afficher plus"
                var buttonSelectors = new[]
                {
                    "button:has-text('Afficher plus')",
                    "button:has-text('Voir plus')",
                    "button:has-text('Charger plus')",
                    "button:has-text('Load more')",
                    "a:has-text('Afficher plus')",
                    "a:has-text('Voir plus')"
                };

                foreach (var selector in buttonSelectors)
                {
                    try
                    {
                        var buttons = await page.QuerySelectorAllAsync(selector);
                        loadMoreButtons.AddRange(buttons);
                    }
                    catch
                    {
                        // Ignorer les erreurs de sélecteur
                    }
                }

                // Méthode alternative : chercher tous les boutons et filtrer par texte
                var allButtons = await page.QuerySelectorAllAsync("button, a[role='button']");

                foreach (var button in allButtons)
                {
                    try
                    {
                        var text = await GetButtonText(button);
                        if (!string.IsNullOrEmpty(text) &&
                            (text.Contains("Afficher plus") || text.Contains("Voir plus") ||
                             text.Contains("Charger plus") || text.Contains("Load more")))
                        {
                            if (!loadMoreButtons.Contains(button))
                            {
                                loadMoreButtons.Add(button);
                            }
                        }
                    }
                    catch
                    {
                        // Ignorer les erreurs de lecture du texte
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Erreur recherche boutons 'Afficher plus' : {ex.Message}");
            }

            return loadMoreButtons;
        }

        /// <summary>
        /// Récupère le texte d'un bouton de manière sécurisée
        /// </summary>
        private async Task<string> GetButtonText(IElementHandle button)
        {
            try
            {
                var textProperty = await button.GetPropertyAsync("textContent");
                var text = await textProperty.JsonValueAsync<string>();
                return text?.Trim() ?? "";
            }
            catch
            {
                return "";
            }
        }

        /// <summary>
        /// Gère la pagination traditionnelle
        /// </summary>
        private async Task HandleTraditionalPagination(IPage page, int maxPages)
        {
            for (int pageNum = 1; pageNum <= maxPages; pageNum++)
            {
                Console.WriteLine($"📄 Analyse IA de la page {pageNum}...");

                // Attendre que la page se charge complètement
                await Task.Delay(5000);

                // Extraire le HTML de la page
                var htmlContent = await page.GetContentAsync();

                // Nettoyer le HTML pour l'IA
                var cleanedHtml = CleanHtmlForAI(htmlContent);

                Console.WriteLine($"📊 HTML nettoyé : {cleanedHtml.Length} caractères");

                // Analyser avec l'IA
                var products = await ExtractProductsWithGemini(cleanedHtml);

                if (products != null && products.Count > 0)
                {
                    _extractedProducts.AddRange(products);
                    Console.WriteLine($"✅ {products.Count} produits extraits par l'IA de la page {pageNum}");
                }
                else
                {
                    Console.WriteLine($"⚠️ Aucun produit extrait de la page {pageNum}");
                }

                // Passer à la page suivante si ce n'est pas la dernière
                if (pageNum < maxPages)
                {
                    var hasNextPage = await NavigateToNextPage(page);
                    if (!hasNextPage)
                    {
                        Console.WriteLine("⚠️ Pas de page suivante trouvée, arrêt de l'extraction");
                        break;
                    }

                    // Délai entre les pages pour simuler navigation humaine
                    var delay = new Random().Next(2000, 8000);
                    Console.WriteLine($"⏳ Attente de {delay / 1000}s avant la page suivante...");
                    await Task.Delay(delay);
                }
            }
        }

        /// <summary>
        /// Navigue vers la page suivante (pagination traditionnelle)
        /// </summary>
        private async Task<bool> NavigateToNextPage(IPage page)
        {
            try
            {
                // Chercher le bouton "Suivant" ou "Next"
                var nextButton = await page.QuerySelectorAsync("a[aria-label*='Suivant'], a[aria-label*='Next'], .a-pagination .a-last a");

                if (nextButton != null)
                {
                    var isVisible = await nextButton.IsVisibleAsync();
                    if (isVisible)
                    {
                        Console.WriteLine("➡️ Navigation vers la page suivante...");
                        await nextButton.ClickAsync();
                        await Task.Delay(3000); // Attendre le chargement
                        return true;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Erreur navigation page suivante : {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Nettoie le HTML pour ne garder que les parties importantes pour l'IA
        /// </summary>
        private string CleanHtmlForAI(string htmlContent)
        {
            // Supprimer les scripts, styles et autres éléments non pertinents
            var cleanedHtml = htmlContent;
            
            // Supprimer les scripts
            cleanedHtml = System.Text.RegularExpressions.Regex.Replace(
                cleanedHtml, @"<script[^>]*>.*?</script>", "", 
                System.Text.RegularExpressions.RegexOptions.IgnoreCase | System.Text.RegularExpressions.RegexOptions.Singleline);
            
            // Supprimer les styles
            cleanedHtml = System.Text.RegularExpressions.Regex.Replace(
                cleanedHtml, @"<style[^>]*>.*?</style>", "", 
                System.Text.RegularExpressions.RegexOptions.IgnoreCase | System.Text.RegularExpressions.RegexOptions.Singleline);
            
            // Supprimer les commentaires HTML
            cleanedHtml = System.Text.RegularExpressions.Regex.Replace(
                cleanedHtml, @"<!--.*?-->", "", 
                System.Text.RegularExpressions.RegexOptions.Singleline);
            
            // Garder seulement les liens vers des produits et leur contexte
            var productSections = ExtractProductSections(cleanedHtml);
            
            return productSections;
        }

        /// <summary>
        /// Extrait les sections HTML qui contiennent des liens vers des produits
        /// </summary>
        private string ExtractProductSections(string html)
        {
            var productSections = new List<string>();

            // Patterns améliorés pour trouver TOUS les liens produits Amazon
            var patterns = new[]
            {
                // Liens directs avec balises complètes
                @"<a[^>]*href[^>]*\/dp\/[A-Z0-9]{10}[^>]*>.*?</a>",
                @"<a[^>]*href[^>]*\/gp\/product\/[A-Z0-9]{10}[^>]*>.*?</a>",

                // URLs dans les attributs href
                @"href=""[^""]*\/dp\/[A-Z0-9]{10}[^""]*""",
                @"href='[^']*\/dp\/[A-Z0-9]{10}[^']*'",

                // Patterns supplémentaires pour capturer plus de liens
                @"\/dp\/[A-Z0-9]{10}",
                @"\/gp\/product\/[A-Z0-9]{10}",
                @"amazon\.fr\/[^\/]*\/dp\/[A-Z0-9]{10}",
                @"data-asin=""[A-Z0-9]{10}""",

                // Liens avec paramètres
                @"href=""[^""]*amazon\.fr[^""]*\/dp\/[A-Z0-9]{10}[^""]*""",
                @"href='[^']*amazon\.fr[^']*\/dp\/[A-Z0-9]{10}[^']*'"
            };

            var allMatches = new List<System.Text.RegularExpressions.Match>();

            foreach (var pattern in patterns)
            {
                var matches = System.Text.RegularExpressions.Regex.Matches(html, pattern,
                    System.Text.RegularExpressions.RegexOptions.IgnoreCase | System.Text.RegularExpressions.RegexOptions.Singleline);
                allMatches.AddRange(matches.Cast<System.Text.RegularExpressions.Match>());
            }

            Console.WriteLine($"🔍 {allMatches.Count} liens produits détectés dans le HTML");

            // OPTIMISATION : Traiter TOUS les produits avec contexte étendu grâce à la limite Gemini élevée
            foreach (var match in allMatches) // Suppression de la limitation Take(20)
            {
                // Pour chaque lien produit, extraire un contexte beaucoup plus large
                var linkPosition = match.Index;
                var contextStart = Math.Max(0, linkPosition - 1500); // Contexte étendu : 1500 caractères avant
                var contextEnd = Math.Min(html.Length, linkPosition + match.Length + 1500); // 1500 caractères après

                var context = html.Substring(contextStart, contextEnd - contextStart);
                productSections.Add($"<!-- PRODUIT {productSections.Count + 1} -->\n{context}\n");
            }

            // OPTIMISATION GOOGLE GEMINI : Utilisation maximale des 1,048,576 tokens disponibles
            // Estimation : ~4 caractères par token, donc ~4,194,304 caractères maximum
            var maxCharacters = 4000000; // Limite sécurisée pour éviter les dépassements

            var result = string.Join("\n", productSections);
            if (result.Length > maxCharacters)
            {
                // Si trop long, prendre les premiers produits complets plutôt que tronquer
                var truncatedSections = new List<string>();
                var currentLength = 0;

                foreach (var section in productSections)
                {
                    if (currentLength + section.Length > maxCharacters)
                        break;
                    truncatedSections.Add(section);
                    currentLength += section.Length;
                }

                result = string.Join("\n", truncatedSections) + "\n<!-- TRONQUÉ POUR RESPECTER LA LIMITE GEMINI -->";
                Console.WriteLine($"⚠️ HTML tronqué à {truncatedSections.Count} produits pour respecter la limite Gemini ({maxCharacters:N0} caractères)");
            }
            else
            {
                Console.WriteLine($"✅ HTML complet envoyé à Gemini : {result.Length:N0} caractères sur {maxCharacters:N0} disponibles");
            }

            Console.WriteLine($"📄 HTML nettoyé pour l'IA : {result.Length} caractères");
            return result;
        }

        /// <summary>
        /// Utilise Gemini pour extraire les informations des produits depuis le HTML
        /// Avec système de retry automatique en cas d'erreur API
        /// </summary>
        private async Task<List<ProductInfo>> ExtractProductsWithGemini(string htmlContent)
        {
            const int maxRetries = 10; // Nombre maximal de tentatives (augmenté)
            const int retryDelayMinutes = 1; // Délai entre les tentatives en minutes

            for (int retryCount = 0; retryCount < maxRetries; retryCount++)
            {
                try
                {
                    if (retryCount > 0)
                    {
                        Console.WriteLine($"🔄 Tentative {retryCount + 1}/{maxRetries} d'appel à l'API Gemini...");
                    }
                    var prompt = @"
🎯 MISSION ULTRA-EXHAUSTIVE : Analyse ce HTML Amazon Deals avec la puissance maximale de Gemini (1M+ tokens)

OBJECTIF PRINCIPAL : Extraire ABSOLUMENT TOUS LES PRODUITS sans aucune exception.

STRATÉGIE D'ANALYSE SYSTÉMATIQUE :

1. RECHERCHE EXHAUSTIVE DES LIENS :
   - Tous les liens /dp/[ASIN] (10 caractères alphanumériques)
   - Tous les liens /gp/product/[ASIN]
   - Liens avec paramètres : ?ref=, ?tag=, etc.
   - Liens relatifs commençant par /dp/ ou /gp/
   - Attributs data-asin=""[ASIN]""

2. EXTRACTION CONTEXTUELLE INTELLIGENTE :
   - Titre du produit dans les balises <span>, <h3>, <h4>, <a>, <div>
   - Texte alt des images
   - Aria-labels et titres d'accessibilité
   - Texte dans les conteneurs parents/enfants

3. DÉTECTION DES PROMOTIONS :
   - Pourcentages : -X%, X% de réduction
   - Badges : ""Économisez"", ""Promo"", ""Deal"", ""Offre""
   - Prix barrés et comparaisons de prix
   - Textes promotionnels

4. VALIDATION ET NETTOYAGE :
   - URLs complètes ou relatives acceptées
   - Titres même partiels ou tronqués
   - Informations de réduction optionnelles

CONSIGNES CRITIQUES :
- NE JAMAIS ignorer un lien /dp/ même si le contexte semble incomplet
- TOUJOURS extraire le maximum d'informations disponibles
- PRÉFÉRER l'inclusion à l'exclusion en cas de doute
- TRAITER chaque section <!-- PRODUIT X --> comme un produit potentiel

FORMAT DE SORTIE STRICT - JSON UNIQUEMENT :
{
  ""products"": [
    {
      ""title"": ""Titre exact ou approximatif du produit"",
      ""url"": ""URL complète ou relative avec /dp/"",
      ""discount"": ""Réduction trouvée ou chaîne vide""
    }
  ]
}

RAPPEL : Avec 1M+ tokens disponibles, ce HTML peut être très volumineux.
Analyse TOUT le contenu méthodiquement, section par section.

HTML VOLUMINEUX À ANALYSER :
" + htmlContent;

                    // Estimation de l'utilisation des tokens
                    var estimatedTokens = prompt.Length / 4; // Approximation : 4 caractères par token
                    var tokenUsagePercent = (estimatedTokens * 100.0) / 1048576; // Pourcentage de la limite Gemini

                    Console.WriteLine("🤖 Envoi à Gemini pour analyse ULTRA-EXHAUSTIVE...");
                    Console.WriteLine($"📊 Estimation tokens : {estimatedTokens:N0} / 1,048,576 ({tokenUsagePercent:F1}%)");
                    Console.WriteLine($"📄 Taille prompt : {prompt.Length:N0} caractères");

                    // Utiliser le générateur de posts qui a déjà la logique Gemini
                    // Assurez-vous que _postGenerator n'est pas null avant d'appeler CallGeminiAPI
                    var response = await (_postGenerator?.CallGeminiAPI(prompt) ?? Task.FromResult("Error: _postGenerator is null"));

                    Console.WriteLine($"📥 Réponse Gemini reçue : {response.Length:N0} caractères");

                    // Vérifier si le quota est épuisé ou si une erreur est survenue
                    if (response.Contains("RESOURCE_EXHAUSTED") || response.Contains("Error:") ||
                        response.Contains("429") || response.Contains("quota") ||
                        response.Contains("rate limit") || response.Contains("billing"))
                    {
                        Console.WriteLine($"❌ Erreur de l'API Gemini détectée : {response.Substring(0, Math.Min(200, response.Length))}...");

                        if (retryCount < maxRetries - 1)
                        {
                            var waitTimeSeconds = retryDelayMinutes * 60;
                            Console.WriteLine($"⏳ Attente de {retryDelayMinutes} minute(s) avant nouvelle tentative... (Tentative {retryCount + 2}/{maxRetries})");
                            Console.WriteLine($"🕐 Prochaine tentative dans {waitTimeSeconds} secondes...");

                            // Attendre avec affichage du compte à rebours
                            for (int i = waitTimeSeconds; i > 0; i -= 10)
                            {
                                Console.WriteLine($"⏳ Attente restante : {i} secondes...");
                                await Task.Delay(TimeSpan.FromSeconds(Math.Min(10, i)));
                            }

                            continue; // Passer à la prochaine itération de la boucle
                        }
                        else
                        {
                            Console.WriteLine($"⚠️ Toutes les {maxRetries} tentatives ont échoué. Passage en mode extraction de secours.");
                            // Mode de secours : extraction basique des liens sans IA
                            var fallbackProducts = ExtractProductsFallback(htmlContent);
                            Console.WriteLine($"🔧 Mode secours : {fallbackProducts.Count} produits extraits sans IA");
                            return fallbackProducts;
                        }
                    }

                    // Parser la réponse JSON
                    var products = ParseGeminiResponse(response);

                    return products; // Retourner les produits si l'extraction réussit
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ Erreur lors de l'analyse IA (tentative {retryCount + 1}/{maxRetries}) : {ex.Message}");

                    if (retryCount < maxRetries - 1)
                    {
                        var waitTimeSeconds = retryDelayMinutes * 60;
                        Console.WriteLine($"⏳ Attente de {retryDelayMinutes} minute(s) avant nouvelle tentative...");
                        Console.WriteLine($"🕐 Prochaine tentative dans {waitTimeSeconds} secondes...");

                        // Attendre avec affichage du compte à rebours
                        for (int i = waitTimeSeconds; i > 0; i -= 10)
                        {
                            Console.WriteLine($"⏳ Attente restante : {i} secondes...");
                            await Task.Delay(TimeSpan.FromSeconds(Math.Min(10, i)));
                        }
                    }
                    else
                    {
                        Console.WriteLine($"⚠️ Toutes les {maxRetries} tentatives ont échoué. Passage en mode extraction de secours.");
                        // Mode de secours en cas d'erreur générale
                        var fallbackProducts = ExtractProductsFallback(htmlContent);
                        Console.WriteLine($"🔧 Mode secours : {fallbackProducts.Count} produits extraits sans IA");
                        return fallbackProducts;
                    }
                }
            }
            return new List<ProductInfo>(); // Devrait être inaccessible, mais pour la complétude
        }

        /// <summary>
        /// Parse la réponse JSON de Gemini
        /// </summary>
        private List<ProductInfo> ParseGeminiResponse(string response)
        {
            try
            {
                // Nettoyer la réponse (enlever les markdown, etc.)
                var cleanResponse = response.Trim();
                if (cleanResponse.StartsWith("```json"))
                {
                    cleanResponse = cleanResponse.Substring(7);
                }
                if (cleanResponse.EndsWith("```"))
                {
                    cleanResponse = cleanResponse.Substring(0, cleanResponse.Length - 3);
                }
                cleanResponse = cleanResponse.Trim();

                // Parser le JSON
                var jsonDoc = JsonDocument.Parse(cleanResponse);
                var productsArray = jsonDoc.RootElement.GetProperty("products");
                
                var products = new List<ProductInfo>();
                
                foreach (var productElement in productsArray.EnumerateArray())
                {
                    var product = new ProductInfo
                    {
                        Title = GetJsonString(productElement, "title"),
                        ProductUrl = GetJsonString(productElement, "url"),
                        Discount = GetJsonString(productElement, "discount"),
                        IsDeal = true,
                        // Laisser les autres champs vides - ils seront remplis plus tard sur la page produit
                        Price = "",
                        OriginalPrice = "",
                        Rating = "",
                        ReviewCount = "",
                        ImageUrl = "",
                        AffiliateLink = ""
                    };

                    // Valider que le produit a au moins un titre et une URL
                    if (!string.IsNullOrEmpty(product.Title) && !string.IsNullOrEmpty(product.ProductUrl))
                    {
                        // Nettoyer l'URL pour s'assurer qu'elle est complète
                        if (product.ProductUrl.StartsWith("/dp/"))
                        {
                            product.ProductUrl = "https://www.amazon.fr" + product.ProductUrl;
                        }

                        products.Add(product);
                        Console.WriteLine($"✅ Produit extrait: {product.Title.Substring(0, Math.Min(50, product.Title.Length))}...");
                    }
                    else
                    {
                        Console.WriteLine($"⚠️ Produit ignoré - Titre: '{product.Title}', URL: '{product.ProductUrl}'");
                    }
                }
                
                Console.WriteLine($"✅ {products.Count} produits parsés depuis la réponse Gemini");
                return products;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur lors du parsing JSON : {ex.Message}");
                Console.WriteLine($"Réponse reçue : {response.Substring(0, Math.Min(500, response.Length))}...");
                return new List<ProductInfo>();
            }
        }

        private string GetJsonString(JsonElement element, string propertyName)
        {
            try
            {
                if (element.TryGetProperty(propertyName, out var property))
                {
                    return property.GetString() ?? "";
                }
                return "";
            }
            catch
            {
                return "";
            }
        }

        /// <summary>
        /// Génère les liens d'affiliation pour tous les produits
        /// </summary>
        public void GenerateAffiliateLinks(string amazonAssociateTag)
        {
            foreach (var product in _extractedProducts)
            {
                if (!string.IsNullOrEmpty(product.ProductUrl))
                {
                    // Extraire l'ASIN de l'URL
                    var asinMatch = System.Text.RegularExpressions.Regex.Match(product.ProductUrl, @"/dp/([A-Z0-9]{10})");
                    if (asinMatch.Success)
                    {
                        var asin = asinMatch.Groups[1].Value;
                        product.AffiliateLink = $"https://www.amazon.fr/dp/{asin}?tag={amazonAssociateTag}";
                    }
                }
            }
        }

        /// <summary>
        /// Méthode de secours pour extraire les produits sans IA (quand quota épuisé)
        /// </summary>
        private List<ProductInfo> ExtractProductsFallback(string htmlContent)
        {
            var products = new List<ProductInfo>();

            try
            {
                // Patterns pour trouver les liens produits Amazon
                var linkPattern = @"href=""([^""]*\/dp\/[A-Z0-9]{10}[^""]*)""";
                var matches = System.Text.RegularExpressions.Regex.Matches(htmlContent, linkPattern);

                Console.WriteLine($"🔧 Mode secours : {matches.Count} liens /dp/ détectés");

                foreach (System.Text.RegularExpressions.Match match in matches.Take(100)) // Limiter à 100 pour éviter la surcharge
                {
                    var url = match.Groups[1].Value;

                    // Nettoyer l'URL
                    if (url.StartsWith("/dp/"))
                    {
                        url = "https://www.amazon.fr" + url;
                    }
                    else if (!url.StartsWith("http"))
                    {
                        url = "https://www.amazon.fr" + url;
                    }

                    // Extraire l'ASIN
                    var asinMatch = System.Text.RegularExpressions.Regex.Match(url, @"/dp/([A-Z0-9]{10})");
                    if (asinMatch.Success)
                    {
                        var asin = asinMatch.Groups[1].Value;

                        // Chercher un titre approximatif dans le contexte
                        var linkPosition = match.Index;
                        var contextStart = Math.Max(0, linkPosition - 500);
                        var contextEnd = Math.Min(htmlContent.Length, linkPosition + 500);
                        var context = htmlContent.Substring(contextStart, contextEnd - contextStart);

                        // Extraire un titre basique
                        var title = ExtractTitleFromContext(context, asin);

                        var product = new ProductInfo
                        {
                            Title = title,
                            ProductUrl = url,
                            Discount = "", // Pas de détection de réduction en mode secours
                            IsDeal = true,
                            Price = "",
                            OriginalPrice = "",
                            Rating = "",
                            ReviewCount = "",
                            ImageUrl = "",
                            AffiliateLink = ""
                        };

                        // Éviter les doublons
                        if (!products.Any(p => p.ProductUrl == product.ProductUrl))
                        {
                            products.Add(product);
                        }
                    }
                }

                Console.WriteLine($"🔧 Mode secours : {products.Count} produits uniques extraits");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur en mode secours : {ex.Message}");
            }

            return products;
        }

        /// <summary>
        /// Extrait un titre approximatif du contexte HTML
        /// </summary>
        private string ExtractTitleFromContext(string context, string asin)
        {
            try
            {
                // Patterns pour trouver des titres
                var titlePatterns = new[]
                {
                    @"alt=""([^""]{10,100})""",
                    @"title=""([^""]{10,100})""",
                    @">([^<]{10,100})<",
                    @"aria-label=""([^""]{10,100})"""
                };

                foreach (var pattern in titlePatterns)
                {
                    var match = System.Text.RegularExpressions.Regex.Match(context, pattern);
                    if (match.Success)
                    {
                        var title = match.Groups[1].Value.Trim();
                        if (title.Length > 10 && !title.Contains("http") && !title.Contains("amazon"))
                        {
                            return title;
                        }
                    }
                }

                // Fallback : utiliser l'ASIN
                return $"Produit Amazon {asin}";
            }
            catch
            {
                return $"Produit Amazon {asin}";
            }
        }

        public List<ProductInfo> GetExtractedProducts() => _extractedProducts;
    }
}
