using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Text.Json;
using System.IO;
using PuppeteerSharp;
using System.Linq;

namespace Amazon2FacebookPoster
{
    /// <summary>
    /// Extracteur IA optimisé avec analyse multimodale (HTML + Screenshots)
    /// </summary>
    public class AiBasedExtractor
    {
        private readonly AmazonLoader<string> _amazonLoader;
        private FacebookPostGenerator? _postGenerator;
        private readonly List<ProductInfo> _extractedProducts = new();
        private readonly Random _random = new();

        // Configuration
        private const string DEALS_URL = "https://www.amazon.fr/deals?ref_=nav_cs_gb";
        private const int MAX_RETRIES = 10;
        private const int RETRY_DELAY_MINUTES = 1;
        private const int MAX_HTML_CHARS = 4_000_000;

        public AiBasedExtractor(AmazonLoader<string> amazonLoader)
        {
            _amazonLoader = amazonLoader;
        }

        public void SetGeminiApiKey(string apiKey) => _postGenerator = new FacebookPostGenerator(apiKey);

        /// <summary>
        /// Point d'entrée principal pour l'extraction IA
        /// </summary>
        public async Task<List<ProductInfo>> ExtractProductsWithAI(int maxPages = 1, int maxLoadMoreClicks = 5)
        {
            ValidateConfiguration();
            _extractedProducts.Clear();

            Console.WriteLine("🤖 Début de l'extraction basée sur l'IA...");
            Console.WriteLine($"📋 Configuration: {maxPages} pages max, {maxLoadMoreClicks} clics 'Afficher plus' max");

            await _amazonLoader.LoadPage(DEALS_URL, async page =>
            {
                var paginationType = await DetectPaginationType(page);
                Console.WriteLine($"🔍 Type de pagination détecté : {paginationType}");

                if (paginationType == "infinite_scroll")
                    await ProcessInfiniteScroll(page, maxPages, maxLoadMoreClicks);
                else
                    await ProcessTraditionalPagination(page, maxPages);

                Console.WriteLine($"🎯 Extraction IA terminée ! Total : {_extractedProducts.Count} produits");
                return "";
            });

            return _extractedProducts;
        }

        /// <summary>
        /// Traite l'infinite scroll avec optimisations
        /// </summary>
        private async Task ProcessInfiniteScroll(IPage page, int maxPages, int maxLoadMoreClicks)
        {
            // Phase 1: Chargement du contenu
            var clickCount = await LoadMoreContent(page, maxLoadMoreClicks);
            
            // Phase 2: Analyse IA par sections
            await AnalyzeContentSections(page, maxPages);
            
            PrintSummary(clickCount, maxLoadMoreClicks, maxPages);
        }

        /// <summary>
        /// Traite la pagination traditionnelle
        /// </summary>
        private async Task ProcessTraditionalPagination(IPage page, int maxPages)
        {
            for (int pageNum = 1; pageNum <= maxPages; pageNum++)
            {
                Console.WriteLine($"📄 Analyse IA de la page {pageNum}...");
                
                await ShowPageInfo(page);
                await Task.Delay(5000);
                
                await AnalyzePage(page, pageNum);
                
                if (pageNum < maxPages && !await NavigateToNextPage(page))
                {
                    Console.WriteLine("⚠️ Pas de page suivante trouvée, arrêt de l'extraction");
                    break;
                }
                
                await RandomDelay(2000, 8000);
            }
        }

        /// <summary>
        /// Charge plus de contenu via les boutons "Afficher plus"
        /// </summary>
        private async Task<int> LoadMoreContent(IPage page, int maxClicks)
        {
            Console.WriteLine($"🔄 PHASE 1: Chargement du contenu (max {maxClicks} clics 'Afficher plus')");
            
            int clickCount = 0;
            while (clickCount < maxClicks)
            {
                if (!await ClickLoadMoreButton(page, clickCount, maxClicks))
                {
                    Console.WriteLine("🔍 Plus de boutons 'Afficher plus' trouvés");
                    break;
                }
                
                clickCount++;
                Console.WriteLine($"✅ Clic {clickCount}/{maxClicks} effectué");
                await Task.Delay(5000);
                await RandomDelay(2000, 5000);
            }
            
            return clickCount;
        }

        /// <summary>
        /// Analyse le contenu par sections
        /// </summary>
        private async Task AnalyzeContentSections(IPage page, int maxPages)
        {
            Console.WriteLine($"🧠 PHASE 2: Analyse IA du contenu chargé (max {maxPages} analyses)");
            
            int stableCount = 0;
            for (int pageNum = 1; pageNum <= maxPages; pageNum++)
            {
                Console.WriteLine($"📄 Analyse IA section {pageNum}/{maxPages}...");
                
                await ScrollToBottom(page);
                var newProducts = await AnalyzePage(page, pageNum);
                
                if (newProducts == 0)
                {
                    stableCount++;
                    Console.WriteLine($"⚠️ Aucun nouveau produit dans cette analyse ({stableCount}/2)");
                    if (stableCount >= 2)
                    {
                        Console.WriteLine("⚠️ Plus de nouveaux produits détectés, arrêt des analyses");
                        break;
                    }
                }
                else
                {
                    stableCount = 0;
                }
                
                if (pageNum < maxPages) await RandomDelay(3000, 8000);
            }
        }

        /// <summary>
        /// Analyse une page et retourne le nombre de nouveaux produits
        /// </summary>
        private async Task<int> AnalyzePage(IPage page, int pageNum)
        {
            Console.WriteLine($"📄 Début de l'analyse de la page/section {pageNum}...");

            // S'assurer que la page est stable avant l'analyse
            await Task.Delay(2000);

            // Capturer le screenshot AVANT d'extraire le HTML pour s'assurer de la cohérence
            var screenshotPath = await CaptureScreenshot(page, pageNum);

            // Extraire le HTML après la capture d'écran
            var htmlContent = await page.GetContentAsync();
            var cleanedHtml = CleanHtmlForAI(htmlContent);

            Console.WriteLine($"📊 HTML nettoyé : {cleanedHtml.Length:N0} caractères");
            if (!string.IsNullOrEmpty(screenshotPath))
            {
                var fileInfo = new FileInfo(screenshotPath);
                Console.WriteLine($"📸 Screenshot capturé : {Path.GetFileName(screenshotPath)} ({fileInfo.Length / 1024:N0} KB)");
            }
            else
            {
                Console.WriteLine("⚠️ Aucun screenshot valide capturé");
            }

            var products = await ExtractProductsWithGemini(cleanedHtml, screenshotPath);
            var newProducts = FilterNewProducts(products);

            _extractedProducts.AddRange(newProducts);

            Console.WriteLine($"✅ {newProducts.Count} nouveaux produits extraits (analyse {pageNum})");
            Console.WriteLine($"📊 Total cumulé : {_extractedProducts.Count} produits uniques");

            ShowProductSamples(newProducts);
            return newProducts.Count;
        }

        /// <summary>
        /// Capture un screenshot optimisé avec attente du chargement complet
        /// </summary>
        private async Task<string> CaptureScreenshot(IPage page, int pageNum)
        {
            try
            {
                Console.WriteLine($"📸 Préparation de la capture d'écran (page {pageNum})...");

                // Attendre que la page soit complètement chargée
                await WaitForPageToLoad(page);

                // Scroll vers le haut pour capturer depuis le début
                await page.EvaluateFunctionAsync("() => window.scrollTo(0, 0)");
                await Task.Delay(2000);

                var screenshotsDir = Path.Combine(Directory.GetCurrentDirectory(), "screenshots");
                Directory.CreateDirectory(screenshotsDir);

                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var filename = $"deals_page_{pageNum}_{timestamp}.png";
                var screenshotPath = Path.Combine(screenshotsDir, filename);

                // Capturer avec options optimisées
                await page.ScreenshotAsync(screenshotPath, new ScreenshotOptions
                {
                    FullPage = true,
                    Type = ScreenshotType.Png
                    // Note: Quality n'est pas supporté pour PNG, seulement pour JPEG
                });

                // Vérifier que le fichier a été créé et n'est pas vide
                var fileInfo = new FileInfo(screenshotPath);
                if (fileInfo.Exists && fileInfo.Length > 1000) // Au moins 1KB
                {
                    Console.WriteLine($"📸 Screenshot sauvegardé : {filename} ({fileInfo.Length / 1024:N0} KB)");
                    return screenshotPath;
                }
                else
                {
                    Console.WriteLine($"⚠️ Screenshot vide ou trop petit : {filename}");
                    return "";
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Erreur capture screenshot : {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// Attend que la page soit complètement chargée avec du contenu visible
        /// </summary>
        private async Task WaitForPageToLoad(IPage page)
        {
            try
            {
                Console.WriteLine("⏳ Attente du chargement complet de la page...");

                // Attendre un délai initial pour le chargement de base
                await Task.Delay(5000);

                // Attendre que les éléments de produits soient visibles
                var maxWaitTime = 30000; // 30 secondes max
                var startTime = DateTime.Now;
                var productCount = 0;

                while ((DateTime.Now - startTime).TotalMilliseconds < maxWaitTime)
                {
                    try
                    {
                        // Vérifier si des produits sont visibles
                        productCount = await page.EvaluateFunctionAsync<int>(@"
                            () => {
                                const products = document.querySelectorAll('a[href*=""/dp/""], [data-asin]');
                                return products.length;
                            }");

                        if (productCount > 5) // Au moins 5 produits pour considérer que la page est chargée
                        {
                            Console.WriteLine($"✅ Page chargée avec {productCount} produits détectés");
                            break;
                        }

                        Console.WriteLine($"⏳ Attente des produits... ({productCount} détectés)");
                        await Task.Delay(3000);
                    }
                    catch
                    {
                        await Task.Delay(3000);
                    }
                }

                // Attendre encore un peu pour les images et CSS
                await Task.Delay(3000);
                Console.WriteLine($"✅ Chargement de la page terminé ({productCount} produits finaux)");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Erreur lors de l'attente du chargement : {ex.Message}");
                // Continuer même en cas d'erreur avec un délai de sécurité
                await Task.Delay(8000);
            }
        }

        /// <summary>
        /// Détecte le type de pagination de manière optimisée
        /// </summary>
        private async Task<string> DetectPaginationType(IPage page)
        {
            try
            {
                var hasPagination = await page.QuerySelectorAsync("a[aria-label*='page'], .a-pagination a, .s-pagination-item") != null;
                if (hasPagination)
                {
                    Console.WriteLine("🔍 Pagination traditionnelle détectée");
                    return "traditional";
                }
                
                var loadMoreCount = await CountLoadMoreButtons(page);
                Console.WriteLine($"🔍 Infinite scroll détecté ({loadMoreCount} boutons 'Voir plus')");
                return "infinite_scroll";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Erreur détection pagination : {ex.Message}");
                return "infinite_scroll";
            }
        }

        /// <summary>
        /// Compte les boutons "Afficher plus" de manière optimisée
        /// </summary>
        private async Task<int> CountLoadMoreButtons(IPage page)
        {
            try
            {
                var buttons = await page.QuerySelectorAllAsync("button, a[role='button']");
                int count = 0;
                
                foreach (var button in buttons)
                {
                    try
                    {
                        var text = await GetElementText(button);
                        if (IsLoadMoreButton(text)) count++;
                    }
                    catch { /* Ignorer */ }
                }
                
                return count;
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// Clique sur un bouton "Afficher plus"
        /// </summary>
        private async Task<bool> ClickLoadMoreButton(IPage page, int currentClick, int maxClicks)
        {
            try
            {
                await ScrollToBottom(page);
                
                if (currentClick >= maxClicks) return false;
                
                var buttons = await page.QuerySelectorAllAsync("button, a[role='button']");
                
                foreach (var button in buttons)
                {
                    try
                    {
                        var text = await GetElementText(button);
                        if (IsLoadMoreButton(text) && await button.IsVisibleAsync())
                        {
                            Console.WriteLine($"🔄 Clic {currentClick + 1}/{maxClicks} sur bouton '{text}'...");
                            await button.ClickAsync();
                            return true;
                        }
                    }
                    catch { /* Continuer */ }
                }
                
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Erreur lors du clic : {ex.Message}");
                return false;
            }
        }

        // Méthodes utilitaires simplifiées
        private void ValidateConfiguration()
        {
            if (_postGenerator == null)
                throw new InvalidOperationException("Clé API Gemini non configurée. Appelez SetGeminiApiKey() d'abord.");
        }

        private async Task ScrollToBottom(IPage page)
        {
            await page.EvaluateFunctionAsync("() => window.scrollTo(0, document.body.scrollHeight)");
            await Task.Delay(2000);
        }

        private async Task RandomDelay(int min, int max)
        {
            var delay = _random.Next(min, max);
            Console.WriteLine($"⏳ Attente de {delay / 1000}s avant la prochaine action...");
            await Task.Delay(delay);
        }

        private async Task<string> GetElementText(IElementHandle element)
        {
            var textProperty = await element.GetPropertyAsync("textContent");
            return (await textProperty.JsonValueAsync<string>())?.Trim() ?? "";
        }

        private static bool IsLoadMoreButton(string text) =>
            !string.IsNullOrEmpty(text) && (text.Contains("Afficher plus") || text.Contains("Voir plus") || 
                                           text.Contains("Charger plus") || text.Contains("Load more"));

        private List<ProductInfo> FilterNewProducts(List<ProductInfo> products) =>
            products.Where(p => !_extractedProducts.Any(existing => 
                existing.ProductUrl == p.ProductUrl || existing.Title == p.Title)).ToList();

        private void ShowProductSamples(List<ProductInfo> newProducts)
        {
            if (newProducts.Count == 0) return;
            
            Console.WriteLine("📋 Nouveaux produits détectés :");
            for (int i = 0; i < Math.Min(3, newProducts.Count); i++)
            {
                var product = newProducts[i];
                Console.WriteLine($"   • {product.Title.Substring(0, Math.Min(60, product.Title.Length))}...");
            }
            if (newProducts.Count > 3)
                Console.WriteLine($"   ... et {newProducts.Count - 3} autres produits");
        }

        private void PrintSummary(int clickCount, int maxClicks, int maxPages)
        {
            Console.WriteLine($"📊 RÉSUMÉ FINAL:");
            Console.WriteLine($"   🔄 Clics 'Afficher plus' effectués: {clickCount}/{maxClicks}");
            Console.WriteLine($"   📦 Total produits uniques: {_extractedProducts.Count}");
        }

        public void GenerateAffiliateLinks(string amazonAssociateTag)
        {
            foreach (var product in _extractedProducts)
            {
                if (!string.IsNullOrEmpty(product.ProductUrl))
                {
                    var asinMatch = System.Text.RegularExpressions.Regex.Match(product.ProductUrl, @"/dp/([A-Z0-9]{10})");
                    if (asinMatch.Success)
                    {
                        var asin = asinMatch.Groups[1].Value;
                        product.AffiliateLink = $"https://www.amazon.fr/dp/{asin}?tag={amazonAssociateTag}";
                    }
                }
            }
        }

        public List<ProductInfo> GetExtractedProducts() => _extractedProducts;

        /// <summary>
        /// Affiche les informations de pagination (pour pagination traditionnelle)
        /// </summary>
        private async Task ShowPageInfo(IPage page)
        {
            try
            {
                var pageInfo = await page.EvaluateFunctionAsync<string>(@"
                    () => {
                        const elements = document.querySelectorAll('*');
                        for (const el of elements) {
                            const text = el.textContent || '';
                            const match = text.match(/Page\s+(\d+)\s+sur\s+(\d+)/i);
                            if (match) return `Page ${match[1]} sur ${match[2]} détectée`;
                        }
                        return '';
                    }");

                if (!string.IsNullOrEmpty(pageInfo))
                    Console.WriteLine($"📄 {pageInfo}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Erreur détection info pagination : {ex.Message}");
            }
        }

        /// <summary>
        /// Navigue vers la page suivante (pagination traditionnelle)
        /// </summary>
        private async Task<bool> NavigateToNextPage(IPage page)
        {
            try
            {
                var nextButton = await page.QuerySelectorAsync("a[aria-label*='Suivant'], a[aria-label*='Next'], .a-pagination .a-last a");
                if (nextButton != null && await nextButton.IsVisibleAsync())
                {
                    Console.WriteLine("➡️ Navigation vers la page suivante...");
                    await nextButton.ClickAsync();
                    await Task.Delay(3000);
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Erreur navigation page suivante : {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Nettoie le HTML pour l'analyse IA (version optimisée)
        /// </summary>
        private string CleanHtmlForAI(string htmlContent)
        {
            // Supprimer scripts, styles et commentaires
            var cleanedHtml = System.Text.RegularExpressions.Regex.Replace(htmlContent,
                @"<script[^>]*>.*?</script>|<style[^>]*>.*?</style>|<!--.*?-->", "",
                System.Text.RegularExpressions.RegexOptions.IgnoreCase | System.Text.RegularExpressions.RegexOptions.Singleline);

            // Extraire les sections de produits
            return ExtractProductSections(cleanedHtml);
        }

        /// <summary>
        /// Extrait les sections HTML contenant des produits (version optimisée)
        /// </summary>
        private string ExtractProductSections(string html)
        {
            var productSections = new List<string>();

            // Patterns optimisés pour les liens produits Amazon
            var patterns = new[]
            {
                @"<a[^>]*href[^>]*\/dp\/[A-Z0-9]{10}[^>]*>.*?</a>",
                @"href=""[^""]*\/dp\/[A-Z0-9]{10}[^""]*""",
                @"data-asin=""[A-Z0-9]{10}""",
                @"\/dp\/[A-Z0-9]{10}"
            };

            var allMatches = new List<System.Text.RegularExpressions.Match>();
            foreach (var pattern in patterns)
            {
                var matches = System.Text.RegularExpressions.Regex.Matches(html, pattern,
                    System.Text.RegularExpressions.RegexOptions.IgnoreCase | System.Text.RegularExpressions.RegexOptions.Singleline);
                allMatches.AddRange(matches.Cast<System.Text.RegularExpressions.Match>());
            }

            Console.WriteLine($"🔍 {allMatches.Count} liens produits détectés dans le HTML");

            // Extraire le contexte pour chaque produit
            foreach (var match in allMatches)
            {
                var linkPosition = match.Index;
                var contextStart = Math.Max(0, linkPosition - 1500);
                var contextEnd = Math.Min(html.Length, linkPosition + match.Length + 1500);
                var context = html.Substring(contextStart, contextEnd - contextStart);
                productSections.Add($"<!-- PRODUIT {productSections.Count + 1} -->\n{context}\n");
            }

            // Optimisation pour respecter la limite Gemini
            var result = string.Join("\n", productSections);
            if (result.Length > MAX_HTML_CHARS)
            {
                var truncatedSections = new List<string>();
                var currentLength = 0;

                foreach (var section in productSections)
                {
                    if (currentLength + section.Length > MAX_HTML_CHARS) break;
                    truncatedSections.Add(section);
                    currentLength += section.Length;
                }

                result = string.Join("\n", truncatedSections) + "\n<!-- TRONQUÉ POUR RESPECTER LA LIMITE GEMINI -->";
                Console.WriteLine($"⚠️ HTML tronqué à {truncatedSections.Count} produits pour respecter la limite Gemini ({MAX_HTML_CHARS:N0} caractères)");
            }
            else
            {
                Console.WriteLine($"✅ HTML complet envoyé à Gemini : {result.Length:N0} caractères sur {MAX_HTML_CHARS:N0} disponibles");
            }

            return result;
        }

        /// <summary>
        /// Extrait les produits avec Gemini (version optimisée avec retry)
        /// </summary>
        private async Task<List<ProductInfo>> ExtractProductsWithGemini(string htmlContent, string screenshotPath = "")
        {
            for (int retryCount = 0; retryCount < MAX_RETRIES; retryCount++)
            {
                try
                {
                    if (retryCount > 0)
                        Console.WriteLine($"🔄 Tentative {retryCount + 1}/{MAX_RETRIES} d'appel à l'API Gemini...");

                    var prompt = CreateOptimizedPrompt(htmlContent);
                    var response = await CallGeminiAPI(prompt, screenshotPath);

                    if (IsErrorResponse(response))
                    {
                        if (retryCount < MAX_RETRIES - 1)
                        {
                            await WaitWithCountdown();
                            continue;
                        }
                        else
                        {
                            Console.WriteLine($"⚠️ Toutes les {MAX_RETRIES} tentatives ont échoué. Passage en mode extraction de secours.");
                            return ExtractProductsFallback(htmlContent);
                        }
                    }

                    return ParseGeminiResponse(response);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ Erreur lors de l'analyse IA (tentative {retryCount + 1}/{MAX_RETRIES}) : {ex.Message}");
                    if (retryCount < MAX_RETRIES - 1)
                        await WaitWithCountdown();
                    else
                        return ExtractProductsFallback(htmlContent);
                }
            }
            return new List<ProductInfo>();
        }

        /// <summary>
        /// Crée un prompt optimisé pour l'analyse IA
        /// </summary>
        private string CreateOptimizedPrompt(string htmlContent)
        {
            return $@"
🎯 MISSION ULTRA-EXHAUSTIVE : Analyse ce HTML Amazon Deals avec screenshot pour une détection parfaite

OBJECTIF PRINCIPAL : Extraire ABSOLUMENT TOUS LES PRODUITS sans aucune exception.

📸 ANALYSE MULTIMODALE (HTML + IMAGE) :
Tu disposes de DEUX sources d'information complémentaires :
1. Le code HTML complet de la page Amazon
2. Un screenshot visuel de la page pour validation et contexte

STRATÉGIE D'ANALYSE RENFORCÉE :
1. RECHERCHE EXHAUSTIVE DES LIENS (HTML) :
   - Tous les liens /dp/[ASIN] (10 caractères alphanumériques)
   - Tous les liens /gp/product/[ASIN]
   - Liens avec paramètres : ?ref=, ?tag=, etc.
   - Attributs data-asin=""[ASIN]""

2. VALIDATION VISUELLE (SCREENSHOT) :
   - Vérifier que les produits détectés dans le HTML sont bien visibles
   - Identifier des produits supplémentaires visibles mais mal structurés dans le HTML
   - Détecter les badges de promotion, prix barrés, pourcentages de réduction

3. EXTRACTION CONTEXTUELLE INTELLIGENTE :
   - Titre du produit dans les balises <span>, <h3>, <h4>, <a>, <div>
   - Texte alt des images et aria-labels
   - Validation avec l'image pour s'assurer de la cohérence

CONSIGNES CRITIQUES :
- UTILISE l'image pour VALIDER et COMPLÉTER les informations du HTML
- NE JAMAIS ignorer un lien /dp/ même si le contexte semble incomplet
- PRÉFÉRER l'inclusion à l'exclusion en cas de doute

FORMAT DE SORTIE STRICT - JSON UNIQUEMENT :
{{
  ""products"": [
    {{
      ""title"": ""Titre exact ou approximatif du produit"",
      ""url"": ""URL complète ou relative avec /dp/"",
      ""discount"": ""Réduction trouvée ou chaîne vide""
    }}
  ]
}}

HTML VOLUMINEUX À ANALYSER :
{htmlContent}";
        }

        /// <summary>
        /// Appelle l'API Gemini avec gestion optimisée des images
        /// </summary>
        private async Task<string> CallGeminiAPI(string prompt, string screenshotPath)
        {
            if (!string.IsNullOrEmpty(screenshotPath) && File.Exists(screenshotPath))
            {
                Console.WriteLine($"📸 Envoi du prompt avec screenshot à Gemini...");
                return await (_postGenerator?.CallGeminiAPIWithImage(prompt, screenshotPath) ?? Task.FromResult("Error: _postGenerator is null"));
            }
            else
            {
                Console.WriteLine($"📝 Envoi du prompt texte seul à Gemini...");
                return await (_postGenerator?.CallGeminiAPI(prompt) ?? Task.FromResult("Error: _postGenerator is null"));
            }
        }

        /// <summary>
        /// Vérifie si la réponse contient une erreur
        /// </summary>
        private bool IsErrorResponse(string response)
        {
            return response.Contains("RESOURCE_EXHAUSTED") || response.Contains("QUOTA_EXHAUSTED") ||
                   response.Contains("Error:") || response.Contains("429") || response.Contains("quota") ||
                   response.Contains("rate limit") || response.Contains("billing");
        }

        /// <summary>
        /// Attend avec compte à rebours optimisé
        /// </summary>
        private async Task WaitWithCountdown()
        {
            var waitTimeSeconds = RETRY_DELAY_MINUTES * 60;
            Console.WriteLine($"⏳ Attente de {RETRY_DELAY_MINUTES} minute(s) avant nouvelle tentative...");
            Console.WriteLine($"🕐 Prochaine tentative dans {waitTimeSeconds} secondes...");

            for (int i = waitTimeSeconds; i > 0; i -= 10)
            {
                Console.WriteLine($"⏳ Attente restante : {i} secondes...");
                await Task.Delay(TimeSpan.FromSeconds(Math.Min(10, i)));
            }
        }

        /// <summary>
        /// Parse la réponse JSON de Gemini (version optimisée)
        /// </summary>
        private List<ProductInfo> ParseGeminiResponse(string response)
        {
            try
            {
                Console.WriteLine($"📥 Réponse Gemini reçue : {response.Length:N0} caractères");

                // Nettoyer la réponse
                var cleanResponse = response.Trim();
                if (cleanResponse.StartsWith("```json")) cleanResponse = cleanResponse.Substring(7);
                if (cleanResponse.EndsWith("```")) cleanResponse = cleanResponse.Substring(0, cleanResponse.Length - 3);
                cleanResponse = cleanResponse.Trim();

                var jsonDoc = JsonDocument.Parse(cleanResponse);
                var productsArray = jsonDoc.RootElement.GetProperty("products");
                var products = new List<ProductInfo>();

                foreach (var productElement in productsArray.EnumerateArray())
                {
                    var product = new ProductInfo
                    {
                        Title = GetJsonString(productElement, "title"),
                        ProductUrl = GetJsonString(productElement, "url"),
                        Discount = GetJsonString(productElement, "discount"),
                        IsDeal = true,
                        Price = "", OriginalPrice = "", Rating = "", ReviewCount = "", ImageUrl = "", AffiliateLink = ""
                    };

                    if (!string.IsNullOrEmpty(product.Title) && !string.IsNullOrEmpty(product.ProductUrl))
                    {
                        if (product.ProductUrl.StartsWith("/dp/"))
                            product.ProductUrl = "https://www.amazon.fr" + product.ProductUrl;

                        products.Add(product);
                        Console.WriteLine($"✅ Produit extrait: {product.Title.Substring(0, Math.Min(50, product.Title.Length))}...");
                    }
                }

                Console.WriteLine($"✅ {products.Count} produits parsés depuis la réponse Gemini");
                return products;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur lors du parsing JSON : {ex.Message}");
                Console.WriteLine($"Réponse reçue : {response.Substring(0, Math.Min(500, response.Length))}...");
                return new List<ProductInfo>();
            }
        }

        /// <summary>
        /// Extraction de secours sans IA (version optimisée)
        /// </summary>
        private List<ProductInfo> ExtractProductsFallback(string htmlContent)
        {
            var products = new List<ProductInfo>();
            try
            {
                var linkPattern = @"href=""([^""]*\/dp\/[A-Z0-9]{10}[^""]*)""";
                var matches = System.Text.RegularExpressions.Regex.Matches(htmlContent, linkPattern);
                Console.WriteLine($"🔧 Mode secours : {matches.Count} liens /dp/ détectés");

                foreach (System.Text.RegularExpressions.Match match in matches.Take(100))
                {
                    var url = match.Groups[1].Value;
                    if (url.StartsWith("/dp/")) url = "https://www.amazon.fr" + url;
                    else if (!url.StartsWith("http")) url = "https://www.amazon.fr" + url;

                    var asinMatch = System.Text.RegularExpressions.Regex.Match(url, @"/dp/([A-Z0-9]{10})");
                    if (asinMatch.Success)
                    {
                        var asin = asinMatch.Groups[1].Value;
                        var title = ExtractTitleFromContext(htmlContent, match.Index, asin);

                        var product = new ProductInfo
                        {
                            Title = title, ProductUrl = url, Discount = "", IsDeal = true,
                            Price = "", OriginalPrice = "", Rating = "", ReviewCount = "", ImageUrl = "", AffiliateLink = ""
                        };

                        if (!products.Any(p => p.ProductUrl == product.ProductUrl))
                            products.Add(product);
                    }
                }

                Console.WriteLine($"🔧 Mode secours : {products.Count} produits uniques extraits");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur en mode secours : {ex.Message}");
            }
            return products;
        }

        /// <summary>
        /// Extrait un titre approximatif du contexte HTML
        /// </summary>
        private string ExtractTitleFromContext(string htmlContent, int linkPosition, string asin)
        {
            try
            {
                var contextStart = Math.Max(0, linkPosition - 500);
                var contextEnd = Math.Min(htmlContent.Length, linkPosition + 500);
                var context = htmlContent.Substring(contextStart, contextEnd - contextStart);

                var titlePatterns = new[] { @"alt=""([^""]{10,100})""", @"title=""([^""]{10,100})""", @">([^<]{10,100})<" };

                foreach (var pattern in titlePatterns)
                {
                    var match = System.Text.RegularExpressions.Regex.Match(context, pattern);
                    if (match.Success)
                    {
                        var title = match.Groups[1].Value.Trim();
                        if (title.Length > 10 && !title.Contains("http") && !title.Contains("amazon"))
                            return title;
                    }
                }
                return $"Produit Amazon {asin}";
            }
            catch
            {
                return $"Produit Amazon {asin}";
            }
        }

        private string GetJsonString(JsonElement element, string propertyName)
        {
            try
            {
                return element.TryGetProperty(propertyName, out var property) ? property.GetString() ?? "" : "";
            }
            catch
            {
                return "";
            }
        }
    }
}
